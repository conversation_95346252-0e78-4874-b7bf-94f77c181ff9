package ai.friday.billpayment.adapters.softwareexpress

import ai.friday.billpayment.app.Acquirer
import ai.friday.billpayment.app.IntegrationError
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.account.CreditCardToken
import ai.friday.billpayment.app.feature.RequiresSoftwareExpress
import ai.friday.billpayment.app.integrations.AcquirerService
import ai.friday.billpayment.app.integrations.GeneralCreditCardConfiguration
import ai.friday.billpayment.app.payment.CreditCardAuthorization
import ai.friday.billpayment.app.payment.UnknownCreditCardPaymentException
import ai.friday.billpayment.log
import ai.friday.billpayment.markers
import ai.friday.billpayment.merge
import ai.friday.billpayment.plus
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.util.*
import kotlin.jvm.optionals.getOrNull
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
@RequiresSoftwareExpress
class SoftwareExpressAdapter(
    @param:Client(
        id = "software-express",
    ) private val httpClient: RxHttpClient,
    @Property(name = "integrations.software-express.credentials")
    private val headers: MutableMap<CharSequence, CharSequence>,
    private val configuration: GeneralCreditCardConfiguration,
) : AcquirerService {
    private val logger = LoggerFactory.getLogger(SoftwareExpressAdapter::class.java)

    private val genericAcquirerErrorReturnMessage = "Ocorreu um erro ao realizar o pagamento"

    override fun authorizeAndCapture(
        accountId: AccountId,
        orderId: String,
        amount: Long,
        creditCard: CreditCard,
        softDescriptor: String,
        installments: Int,
    ) = authorize(
        accountId = accountId,
        orderId = formatOrderId(orderId),
        amount = amount,
        creditCard = creditCard,
        softDescriptor = formatSoftDescriptor(softDescriptor),
        installments = installments,
        capture = true,
    )

    override fun authorize(
        accountId: AccountId,
        orderId: String,
        amount: Long,
        creditCard: CreditCard,
        softDescriptor: String,
        installments: Int,
    ) = authorize(
        accountId = accountId,
        orderId = formatOrderId(orderId),
        amount = amount,
        creditCard = creditCard,
        softDescriptor = formatSoftDescriptor(softDescriptor),
        installments = installments,
        capture = false,
    )

    override fun capture(acquirerTid: String) = settlePayment(acquirerTid = acquirerTid, capture = true)

    override fun cancel(orderId: String) {
        val formattedOrderId = formatOrderId(orderId)
        val markers = append("orderId", formattedOrderId)
        findByOrderId(formattedOrderId)
            .onSuccess { response -> settlePayment(acquirerTid = response.nit, capture = false) }
            .onFailure { error ->
                when (error) {
                    is HttpClientResponseException -> {
                        logger.error(markers.merge(error.markers()), "SoftwareExpressCancel")
                    }

                    else -> logger.error(markers, "SoftwareExpressCancel", error)
                }
            }
    }

    override fun checkStatus(orderId: String): CreditCardAuthorization {
        val formattedOrderId = formatOrderId(orderId)
        return findByOrderId(formattedOrderId)
            .getOrElse { error ->
                when (error) {
                    is HttpClientResponseException -> {
                        val body = error.response.getBody(ErrorTO::class.java)

                        if (body.isPresent && body.get().isTransactionNotFound()) {
                            return CreditCardAuthorization(
                                acquirer = Acquirer.SOFTWARE_EXPRESS,
                                transactionId = formattedOrderId,
                                status = CreditCardPaymentStatus.DENIED,
                                acquirerReturnMessage = "",
                            )
                        }

                        return CreditCardAuthorization(
                            acquirer = Acquirer.SOFTWARE_EXPRESS,
                            transactionId = formattedOrderId,
                            status = CreditCardPaymentStatus.ABORTED,
                            acquirerReturnMessage = genericAcquirerErrorReturnMessage,
                        )
                    }

                    else -> {
                        return CreditCardAuthorization(
                            acquirer = Acquirer.SOFTWARE_EXPRESS,
                            transactionId = formattedOrderId,
                            status = CreditCardPaymentStatus.ABORTED,
                            acquirerReturnMessage = genericAcquirerErrorReturnMessage,
                        )
                    }
                }
            }
            .toCreditCardAuthorization()
    }

    override fun tokenize(
        cardNumber: String,
        expirationDate: String,
        brand: CreditCardBrand,
        holderName: String,
        uid: String,
    ): Either<IntegrationError, CreditCardToken> {
        val requestTO = buildTokenizeRequest(cardNumber, expirationDate, brand, generateCustomerId(uid))

        val markers = requestTO.markers().plus("bin" to cardNumber.take(6))

        val request = HttpRequest.POST("/e-sitef/api/v1/cards", requestTO).headers(buildHeaders(headers))

        try {
            val response =
                httpClient.toBlocking().exchange(request, Argument.of(TokenizeResponseTO::class.java), Argument.STRING)

            logger.info(
                markers.merge(response.body().markers()).plus("status" to response.status),
                "SoftwareExpressTokenize",
            )

            if (response.body().code != "0") {
                logger.error(
                    markers.plus(
                        "response" to response.body().message,
                        "status" to response.status,
                    ),
                    "SoftwareExpressTokenize",
                )

                return IntegrationError.ClientError().left()
            }

            return CreditCardToken(response.body().card.token).right()
        } catch (e: HttpClientResponseException) {
            logger.error(markers.merge(e.markers()), "SoftwareExpressTokenize")

            return handleHttpResponseError(e).left()
        } catch (e: Exception) {
            logger.error(markers, "SoftwareExpressTokenize", e)

            return IntegrationError.ServerError(e).left()
        }
    }

    override fun validate(token: CreditCardToken, cvv: String): Either<IntegrationError, Boolean> {
        val response = callPayment(
            orderId = UUID.randomUUID().toString(),
            amount = 0,
            softDescriptor = configuration.challenge.softDescriptor,
            installments = 1,
            token = token,
            cvv = cvv,
            capture = true,
        ).getOrElse { return it.left() }

        return (response.payment.status == PaymentStatus.CON).right()
    }

    fun authorizeWalletPayment(
        orderId: String,
        walletTransactionId: String,
        walletReturnedCardBrand: String,
        amount: String,
    ): CreditCardAuthorization {
        val nit = createTransaction(orderId = orderId, amount = amount).getOrThrow() // TODO

        val result = callWalletPayment(
            nit = nit,
            walletTransactionId = walletTransactionId,
            walletReturnedCardBrand = walletReturnedCardBrand,
        ).getOrElse { error ->
            when (error) {
                is IntegrationError.ClientError -> {
                    val message = error.e?.response?.getBody(ErrorTO::class.java)?.getOrNull()?.message

                    return CreditCardAuthorization(
                        acquirer = Acquirer.SOFTWARE_EXPRESS,
                        transactionId = orderId,
                        status = if (message != null) CreditCardPaymentStatus.DENIED else CreditCardPaymentStatus.ABORTED,
                        acquirerReturnMessage = message ?: "",
                    )
                }

                is IntegrationError.ServerError -> {
                    return CreditCardAuthorization(
                        acquirer = Acquirer.SOFTWARE_EXPRESS,
                        transactionId = orderId,
                        status = CreditCardPaymentStatus.ABORTED,
                        acquirerReturnMessage = genericAcquirerErrorReturnMessage,
                    )
                }
            }
        }

        return result.payment.toCreditCardAuthorization()
    }

    /**
     * Creates a transaction and returns the NIT (Network Identification Terminal)
     *
     * @param merchantUsn Merchant unique sequence number
     * @param orderId Order identifier
     * @param installments Number of installments
     * @param installmentType Type of installment (default: 4 - no interest)
     * @param authorizerId Authorizer identifier
     * @param amount Transaction amount in cents
     * @return Either an IntegrationError or the transaction response containing the NIT
     */
    private fun createTransaction(
        orderId: String,
        amount: String,
    ): Result<String> {
        val requestTO = TransactionRequestTO(
            orderId = orderId,
            amount = amount,
        )

        val markers = requestTO.markers()
        val request = HttpRequest
            .POST("/e-sitef/api/v1/transactions", requestTO)
            .headers(buildHeaders(headers))

        try {
            val response = httpClient.toBlocking()
                .exchange(request, Argument.of(TransactionResponseTO::class.java), Argument.STRING)

            logger.info(markers.merge(response.markers()), "SoftwareExpressCreateTransaction")

            if (response.body().code != "0") {
                logger.error(
                    markers.plus(
                        "response" to response.body().message,
                        "status" to response.status,
                    ),
                    "SoftwareExpressCreateTransaction",
                )

                return Result.failure(IllegalStateException("Unexpected response: ${response.body().message}"))
            }

            return Result.success(response.body.get().payment.nit)
        } catch (e: HttpClientResponseException) {
            logger.error(markers.merge(e.markers()), "SoftwareExpressCreateTransaction")

            return Result.failure(e)
        } catch (e: Exception) {
            logger.error(markers, "SoftwareExpressCreateTransaction", e)

            return Result.failure(e)
        }
    }

    private fun generateCustomerId(uid: String) = uid.replace(Regex("\\D*"), "").take(20)

    private fun findByOrderId(orderId: String): Result<PaymentTO> {
        val request =
            HttpRequest.GET<Unit>("/e-sitef/api/v2/transactions?order_id=$orderId").headers(buildHeaders(headers))

        return runCatching {
            val response = httpClient.toBlocking()
                .exchange(request, Argument.of(PaymentSearchResponseTO::class.java), Argument.STRING)

            logger.info(response.markers().plus("orderId" to orderId), "SoftwareExpressFindByOrderId")

            response.body().transactions.single { it.payment.orderId == orderId }.payment
        }.onFailure { e ->
            when (e) {
                is HttpClientResponseException -> {
                    logger.error(e.response.markers(), "SoftwareExpressFindByOrderId")
                }

                else -> logger.error(log("orderId" to orderId), "SoftwareExpressFindByOrderId", e)
            }
        }
    }

    // Tam max de 40 caracteres
    private fun formatOrderId(orderId: String): String = orderId.takeLast(36)

    private fun authorize(
        accountId: AccountId,
        orderId: String,
        amount: Long,
        creditCard: CreditCard,
        softDescriptor: String,
        installments: Int,
        capture: Boolean,
    ): CreditCardAuthorization {
        val result = callPayment(
            orderId = orderId,
            amount = amount,
            token = creditCard.token ?: error("Token is required"),
            cvv = creditCard.securityCode,
            softDescriptor = softDescriptor,
            installments = installments,
            capture = capture,
        ).getOrElse { error ->
            when (error) {
                is IntegrationError.ClientError -> {
                    val message = error.e?.response?.getBody(ErrorTO::class.java)?.getOrNull()?.message

                    return CreditCardAuthorization(
                        acquirer = Acquirer.SOFTWARE_EXPRESS,
                        transactionId = orderId,
                        status = if (message != null) CreditCardPaymentStatus.DENIED else CreditCardPaymentStatus.ABORTED,
                        acquirerReturnMessage = message ?: "",
                    )
                }

                is IntegrationError.ServerError -> {
                    return CreditCardAuthorization(
                        acquirer = Acquirer.SOFTWARE_EXPRESS,
                        transactionId = orderId,
                        status = CreditCardPaymentStatus.ABORTED,
                        acquirerReturnMessage = genericAcquirerErrorReturnMessage,
                    )
                }
            }
        }

        return result.payment.toCreditCardAuthorization()
    }

    private fun settlePayment(acquirerTid: String, capture: Boolean) {
        val request = HttpRequest.PUT("/e-sitef/api/v2/payments/$acquirerTid?confirm=$capture", null)
            .headers(buildHeaders(headers))

        val markers = request.markers()

        try {
            val httpResponse = httpClient.toBlocking()
                .exchange(request, Argument.of(PaymentConfirmationResponseTO::class.java), Argument.STRING)

            val status = httpResponse.body().payment.status

            if (paymentNotSettled(capture, status)) {
                throw UnknownCreditCardPaymentException(IllegalStateException("Unknown status $status"))
            }

            logger.info(markers.merge(httpResponse.markers()), "SoftwareExpressConfirmPayment")
        } catch (e: HttpClientResponseException) {
            logger.error(markers.merge(e.markers()), "SoftwareExpressConfirmPayment")

            throw UnknownCreditCardPaymentException(e)
            // FIXME inicialmente vale jogar esse para todos os casos e depois vamos adicionando casos aqui.
            //  https://dev.softwareexpress.com.br/en/docs/e-sitef/codigos-da-api-resposta#response-codes
        } catch (e: Exception) {
            logger.error(markers, "SoftwareExpressConfirmPayment", e)

            throw UnknownCreditCardPaymentException(e)
        }
    }

    private fun paymentNotSettled(capture: Boolean, status: PaymentStatus) =
        (capture && status != PaymentStatus.CON) || (!capture && status != PaymentStatus.PPN)

    private fun callPayment(
        accountId: AccountId? = null,
        orderId: String,
        amount: Long,
        token: CreditCardToken,
        cvv: String?,
        softDescriptor: String,
        installments: Int,
        capture: Boolean,
    ): Either<IntegrationError, PaymentResponseTO> {
        val requestTO = buildPaymentRequest(
            orderId = orderId,
            amount = amount.toString(),
            softDescriptor = configuration.softDescriptorPrefix.orEmpty() + softDescriptor,
            installments = installments.toString(),
            token = token,
            cvv = cvv,
            capture = capture,
        )

        val markers = requestTO.markers().plus("accountId" to accountId?.value)
        val request = HttpRequest
            .POST("/e-sitef/api/v2/payments/", requestTO)
            .headers(buildHeaders(headers))

        try {
            val response =
                httpClient.toBlocking().exchange(request, Argument.of(PaymentResponseTO::class.java), Argument.STRING)

            logger.info(markers.merge(response.markers()), "SoftwareExpressPayment")

            return response.body().right()
        } catch (e: HttpClientResponseException) {
            logger.error(markers.merge(e.markers()), "SoftwareExpressPayment")

            return handleHttpResponseError(e).left()
        } catch (e: Exception) {
            logger.error(markers, "SoftwareExpressPayment", e)

            return IntegrationError.ServerError(e).left()
        }
    }

    private fun callWalletPayment(
        nit: String,
        walletTransactionId: String,
        walletReturnedCardBrand: String,
    ): Either<IntegrationError, PaymentResponseTO> {
        val authorizerId = "405" // Valor fixo para Google Pay

        val requestTO = GooglePayRequestTO(
            authorizerId = authorizerId,
            card = GooglePayCardTO(
                walletTransactionId = walletTransactionId,
                walletReturnedCardBrand = walletReturnedCardBrand,
            ),
        )

        val markers = requestTO.markers()
        val request = HttpRequest
            .POST("/e-sitef/api/v1/payments/$nit", requestTO)
            .headers(buildHeaders(headers))

        try {
            val response =
                httpClient.toBlocking().exchange(request, Argument.of(PaymentResponseTO::class.java), Argument.STRING)

            logger.info(markers.merge(response.markers()), "SoftwareExpressWalletPayment")

            return response.body().right()
        } catch (e: HttpClientResponseException) {
            logger.error(markers.merge(e.markers()), "SoftwareExpressWalletPayment")

            return handleHttpResponseError(e).left()
        } catch (e: Exception) {
            logger.error(markers, "SoftwareExpressWalletPayment", e)

            return IntegrationError.ServerError(e).left()
        }
    }
}

private fun handleHttpResponseError(e: HttpClientResponseException) =
    when (e.status.code) {
        in (400..499) -> IntegrationError.ClientError(e)

        else -> IntegrationError.ServerError()
    }

private fun buildPaymentRequest(
    orderId: String,
    amount: String,
    softDescriptor: String,
    installments: String,
    token: CreditCardToken,
    cvv: String?,
    capture: Boolean,
) =
    PaymentRequestTO(
        orderId = orderId,
        installments = installments,
        installmentType = INSTALLMENT_TYPE,
        amount = amount,
        softDescriptor = softDescriptor,
        card = PaymentCardTO(token = token.value, securityCode = cvv),
        additionalData = if (capture) null else AdditionalDataTO(postponeConfirmation = "true"),
    )

private fun buildHeaders(headers: MutableMap<CharSequence, CharSequence>): MutableMap<CharSequence, CharSequence?> =
    mutableMapOf(
        "Content-Type" to MediaType.APPLICATION_JSON,
        "merchant_id" to headers["merchant-id"],
        "merchant_key" to headers["merchant-key"],
    )

private fun buildTokenizeRequest(cardNumber: String, expirationDate: String, brand: CreditCardBrand, uid: String) =
    TokenizeRequestTO(
        card = CardTO(
            number = cardNumber,
            expiryDate = formatExpiryDate(expirationDate),
        ),
        authorizerId = authorizerId.getValue(brand),
        customerId = uid,
    )

private fun formatExpiryDate(expirationDate: String) = expirationDate.take(2) + expirationDate.takeLast(2)

private val authorizerId: Map<CreditCardBrand, String> = mapOf(
    CreditCardBrand.VISA to "1",
    CreditCardBrand.MASTERCARD to "2",
    CreditCardBrand.AMEX to "3",
    CreditCardBrand.HIPERCARD to "5",
    CreditCardBrand.AURA to "6",
    CreditCardBrand.DINERS to "33",
    CreditCardBrand.ELO to "41",
    CreditCardBrand.JCB to "43",
    CreditCardBrand.DISCOVER to "44",
)

private fun PaymentTO.toCreditCardAuthorization() = CreditCardAuthorization(
    acquirerTid = nit,
    authorizationCode = authorizationNumber,
    amount = amount.toLong(),
    status = when (status) {
        PaymentStatus.CON -> CreditCardPaymentStatus.PAYMENT_CONFIRMED
        PaymentStatus.PPC -> CreditCardPaymentStatus.AUTHORIZED
        PaymentStatus.NEG, PaymentStatus.ERR -> CreditCardPaymentStatus.DENIED
        PaymentStatus.PPN -> CreditCardPaymentStatus.VOIDED
        PaymentStatus.EST -> CreditCardPaymentStatus.REFUNDED
        PaymentStatus.ABA -> CreditCardPaymentStatus.ABORTED
        else -> error("Status not mapped on our domain status: $status")
    },
    acquirer = Acquirer.SOFTWARE_EXPRESS,
    transactionId = orderId,
    acquirerReturnCode = authorizerCode,
    acquirerReturnMessage = authorizerMessage,
    tid = nit,
    originalAcquirer = acquirerName,
)

private fun formatSoftDescriptor(softDescriptor: String) = softDescriptor.replace("[^a-zA-Z0-9]".toRegex(), "").take(13) // max 13 chars

@Suppress("unused")
private enum class PaymentStatus {
    NOV, /* Nova  */
    INV, /* Inválida  */
    PPC, /* Pendente de confirmação  */
    PPN, /* Desfeita  */
    PEN, /* Aguardando  */
    CON, /* Efetuada  */
    NEG, /* Negada  */
    ERR, /* Erro  */
    BLQ, /* Bloqueada  */
    EXP, /* Expirada  */
    EST, /* Estornada  */
    AGU, /* Aguardando  */
    ABA, /* Abandonada  */
    CAN, /* Cancelada  */
    RET, /* Retentativa  */
}

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class PaymentSearchResponseTO(
    val code: String,
    val message: String,
    val transactions: List<TransactionPaymentTO>,
    val currentPage: String,
    val totalPages: String,
    val count: String,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class TransactionPaymentTO(
    val payment: PaymentTO,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class PaymentResponseTO(
    val code: String,
    val message: String,
    val payment: PaymentTO,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class PaymentConfirmationResponseTO(
    val code: String,
    val message: String,
    val payment: PaymentConfirmationTO,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class PaymentConfirmationTO(
    val authorizerCode: String?,
    val status: PaymentStatus,
    val acquirerId: String?,
    val hostUsn: String?,
    val paymentDate: String?,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonIgnoreProperties(ignoreUnknown = true)
private data class PaymentTO(
    val authorizerCode: String?,
    val authorizerMessage: String,
    val status: PaymentStatus,
    val nit: String,
    val orderId: String,
    val customerReceipt: String?,
    val merchantReceipt: String?,
    val authorizerId: String?,
    val acquirerId: String?,
    val acquirerName: String?,
    val authorizerDate: String?,
    val authorizationNumber: String?,
    val merchantUsn: String?,
    val esitefUsn: String?,
    val sitefUsn: String?,
    val hostUsn: String?,
    val amount: String,
    val paymentType: String?,
    val issuer: String?,
    val authorizerMerchantId: String?,
    val terminalId: String?,
    val paymentDate: String?,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class TokenizeRequestTO(
    val card: CardTO,
    val authorizerId: String,
    val customerId: String?,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class CardTO(
    val number: String,
    val expiryDate: String,
)

private data class TokenizeResponseTO(
    val code: String,
    val message: String,
    val card: TokenizedCardTO,
    val store: StoreTO,
)

private data class TokenizedCardTO(
    val token: String,
    val suffix: String?,
    val bin: String?,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class StoreTO(
    val status: String,
    val nsua: String?,
    val nita: String?,
    val merchantUsn: String?,
    val customerId: String?,
    val authorizerId: String?,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class PaymentCardTO(
    val token: String,
    @JsonInclude(JsonInclude.Include.NON_NULL)
    val securityCode: String?,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@Suppress("unused")
private class PaymentRequestTO(
    val orderId: String,
    val installments: String,
    val installmentType: String,
    val amount: String,
    val softDescriptor: String,
    val card: PaymentCardTO,
    val additionalData: AdditionalDataTO?,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private class AdditionalDataTO(
    val postponeConfirmation: String,
)

private class ErrorTO(
    val code: String,
    val message: String,
)

private fun ErrorTO.isTransactionNotFound() = code == "82"

private fun PaymentRequestTO.markers() = markers(
    "orderId" to orderId,
    "amount" to amount,
    "softDescriptor" to softDescriptor,
    "installmentType" to installmentType,
    "installments" to installments,
    "capture" to additionalData?.postponeConfirmation,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class GooglePayCardTO(
    @JsonProperty("wallet_transaction_id")
    val walletTransactionId: String,
    @JsonProperty("wallet_returned_card_brand")
    val walletReturnedCardBrand: String,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class GooglePayRequestTO(
    @JsonProperty("authorizer_id")
    val authorizerId: String,
    val card: GooglePayCardTO,
)

private fun GooglePayRequestTO.markers() = markers(
    "authorizerId" to authorizerId,
    "walletTransactionId" to card.walletTransactionId.take(20),
    "walletReturnedCardBrand" to card.walletReturnedCardBrand,
)

private fun TokenizeRequestTO.markers() = markers(
    "bin" to card.number.take(6),
    "expiryDate" to card.expiryDate,
    "authorizerId" to authorizerId,
    "customerId" to customerId,
)

private fun TokenizeResponseTO.markers() = markers(
    "responseTokenEnd" to card.token.takeLast(3),
    "responseSuffix" to card.suffix,
    "responseBin" to card.bin,
    "responseMessage" to message,
    "responseCode" to code,
    "responseStore" to store,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class TransactionRequestTO(
    val orderId: String,
    val installments: String = "1",
    val installmentType: String = "4",
    val amount: String,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class TransactionResponseTO(
    val code: String,
    val message: String,
    val payment: PaymentTO,
)

private fun TransactionRequestTO.markers() = markers(
    "orderId" to orderId,
    "installments" to installments,
    "installmentType" to installmentType,
    "amount" to amount,
)

/*
 3 = parcelamento com juros da administradora do cartão.
 4 = parcelamento realizado pela loja e sem juros (adotar este valor como padrão/default para transações à vista).
 6 = parcelamento com juros da administradora (IATA).
 7 = parcelamento realizado pela loja e sem juros (IATA).
 O parcelamento IATA é utilizado somente por empresas do seguimento de transporte aéreo.
*/
private const val INSTALLMENT_TYPE = "4"