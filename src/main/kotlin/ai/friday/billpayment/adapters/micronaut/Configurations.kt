package ai.friday.billpayment.adapters.micronaut

import ai.friday.billpayment.app.account.TokenOnboardingConfiguration
import ai.friday.billpayment.app.integrations.BlipConfiguration
import ai.friday.billpayment.app.integrations.CreditCardChallengeConfiguration
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.GeneralCreditCardConfiguration
import ai.friday.billpayment.app.integrations.NotificationHintConfiguration
import ai.friday.billpayment.app.integrations.TedConfiguration
import ai.friday.billpayment.app.integrations.WalletConfiguration
import ai.friday.billpayment.app.recurrence.RecurrenceConfiguration
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties("token.onboarding")
class TokenOnboardingConfigurationMicronaut
@ConfigurationInject constructor(
    override val tokenSize: Int,
    override val maxCooldownDuration: Long,
    override val phoneVerificationDuration: Long,
    override val emailVerificationDuration: Long,
    override val whatsappVerificationDuration: Long,
    override val passwordRecoveryDuration: Long,
    override val livenessVerificationDuration: Long,
    override val maxErrorCount: Int,
    override val message: String,
    override val testMobilePhones: List<String>,
    override val appleMobilePhones: List<String>,
    override val appleTokenVerify: String,
) : TokenOnboardingConfiguration

@ConfigurationProperties("recurrence")
class RecurrenceConfigurationMicronaut
@ConfigurationInject constructor(
    override val lastLimitDate: String,
    override val limitDate: String,
) : RecurrenceConfiguration

@ConfigurationProperties("bill.ted")
class TedConfigurationMicronaut
@ConfigurationInject constructor(
    override val limitTime: String,
) : TedConfiguration

@ConfigurationProperties("features")
class FeaturesConfigurationMicronaut
@ConfigurationInject constructor(
    override val asyncSettlement: Boolean,
    override val forwardEmailToManualWorkflow: Boolean,
    override val automaticUserRegister: Boolean,
    override val zeroAuthEnabled: Boolean,
    override val blockDuplicateByIdNumber: Boolean,
    override val userPilotEnabled: Boolean,
    override val ddaBatchAddAsync: Boolean,
    override val silenceNotifications: Boolean,
    override val skipFullImportWhenAccountHasBills: Boolean,
    override val creditCardChallenge: Boolean,
    override val optOutNotification: Boolean,
    override val creditCardQuota: Boolean,
    override val updateScheduleOnAmountLowered: Boolean,
    override val updateAmountAfterPaymentWindow: Boolean,
    override val imageReceipt: Boolean,
    override val sendEmailViaApiFridayAi: Boolean,
) : FeatureConfiguration

@ConfigurationProperties("creditCard")
data class CreditCardConfigurationMicronaut @ConfigurationInject
constructor(
    override val periodLimit: Duration,
    override val maxPeriodLimit: Int,
    override val maxLimit: Int,
    override val softDescriptorPrefix: String?,
    override val challenge: ChallengeConfiguration,
) : GeneralCreditCardConfiguration {
    @ConfigurationProperties("challenge")
    data class ChallengeConfiguration @ConfigurationInject constructor(
        override val softDescriptor: String,
        override val expiration: Duration,
        override val maxAttempts: Int,
        override val minAmount: Long,
        override val maxAmount: Long,
    ) : CreditCardChallengeConfiguration
}

@ConfigurationProperties("wallet")
class WalletConfigurationMicronaut @ConfigurationInject constructor(
    override val maxOpenInvites: Int,
    override val inviteExpiration: Duration,
) : WalletConfiguration

@ConfigurationProperties("notificationHints")
class NotificationHintConfigurationMicronaut @ConfigurationInject constructor(
    override val billCreated: List<String>,
    override val billComingDue: List<String>,
    override val billComingDueLastWarn: List<String>,
    override val billOverdueYesterday: List<String>,
) : NotificationHintConfiguration

@ConfigurationProperties("blip-callback")
class BlipConfigurationMicronaut @ConfigurationInject constructor(
    override val allowedIps: List<String>,
) : BlipConfiguration